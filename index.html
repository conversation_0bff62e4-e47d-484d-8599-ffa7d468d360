<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合论网络 - 新媒体传播及数字化营销领军者</title>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/fontawesome-free-5.15.4-web/css/all.min.css">
    <!-- 添加GSAP -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/gsap/gsap.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/gsap/ScrollTrigger.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/gsap/ScrollToPlugin.min.js"></script>
    <style>
        @font-face {
            font-family: '思源黑体';
            src: url(https://ztimg.hefei.cc/static/common/fonts/思源黑体.otf);
        }
        *{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        html,body{
            overflow-x: hidden;
            font-family: '思源黑体';
        }
        /* 导航栏样式 */
        .nav-blur {
            backdrop-filter: blur(10px);
            background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
            border-bottom: 1px solid rgba(0, 102, 255, 0.1);
        }

        .nav-link {
            position: relative;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(45deg, #0066FF, #9D00FF);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .nav-link.active {
            color: #0066FF;
        }

        .nav-link.active::after {
            width: 100%;
        }

        /* 移动端菜单按钮 */
        .menu-button {
            display: none;
            padding: 0.5rem;
            border: none;
            background: none;
            cursor: pointer;
        }

        .menu-button span {
            display: block;
            width: 25px;
            height: 2px;
            background: #333;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        @media (max-width: 768px) {
            .menu-button {
                display: block;
            }
            
            .nav-menu {
                position: fixed;
                top: 0;
                right: -100%;
                width: 80%;
                height: 100vh;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                padding: 2rem;
                transition: all 0.3s ease;
            }
            
            .nav-menu.active {
                right: 0;
            }
            
            .nav-menu a {
                display: block;
                padding: 1rem 0;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            }
        }

        /* 按钮样式 */
        .tech-button {
            background: linear-gradient(45deg, #0066FF, #9D00FF);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }

        .tech-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 102, 255, 0.3);
        }

        /* 动画元素初始状态 */
        #news, #wansao, #qichuang, #about-card {
            opacity: 0;
        }
        #news {
            transform: translateX(50px);
        }
        #about-card {
            transform: translateX(-50px);
        }
        #wansao {
            transform: translateX(-50px);
        }
        #qichuang {
            transform: translateX(50px);
        }

        /* 四大能力初始状态 */
        #capabilities h2,
        #capabilities button {
            opacity: 0;
            transform: translateY(30px);
        }

        /* 集团新闻特殊样式 */
        .news-timeline {
            position: relative;
        }

        .news-timeline::before {
            content: '';
            position: absolute;
            left: -1px;
            top: 10px;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #3b82f6, #8b5cf6, #ef4444, #10b981, #f59e0b);
            border-radius: 1px;
        }

        .news-item {
            position: relative;
            transition: all 0.3s ease;
        }

        .news-item:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
            border-radius: 8px;
            /* padding: 8px;
            margin: -8px; */
        }

        .news-dot {
            position: relative;
            z-index: 2;
        }

        .news-dot::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .news-item:hover .news-dot::after {
            opacity: 1;
        }

        /* 新闻卡片悬停效果 */
        .news-card {
            position: relative;
            overflow: hidden;
        }

        .news-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .news-card:hover::before {
            left: 100%;
        }
    </style>
</head>
<body>

    <!-- 导航栏 -->
    <nav class="nav-blur shadow-lg fixed w-full z-50 transition-all duration-300">
        <div class="container mx-auto px-6 py-3">
            <div class="flex items-center justify-between">
                <div class="text-xl font-bold text-gray-800">
                    <img src="img/logo.png" class="h-6 hover:opacity-80 transition-opacity duration-300">
                </div>
                <button class="menu-button md:hidden">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <div class="hidden md:flex space-x-8 nav-menu">
                    <a href="#" class="nav-link text-gray-800 hover:text-blue-600 transition-colors duration-300 active">首页</a>
                    <a href="#about" class="nav-link text-gray-800 hover:text-blue-600 transition-colors duration-300">关于我们</a>
                    <a href="#news" class="nav-link text-gray-800 hover:text-blue-600 transition-colors duration-300">集团新闻</a>
                    <a href="#capabilities" class="nav-link text-gray-800 hover:text-blue-600 transition-colors duration-300">四大能力</a>
                    <a href="#" class="nav-link text-gray-800 hover:text-blue-600 transition-colors duration-300">合肥论坛</a>
                    <a href="#" class="nav-link text-gray-800 hover:text-blue-600 transition-colors duration-300">合肥热线</a>
                    <a href="#services" class="nav-link text-gray-800 hover:text-blue-600 transition-colors duration-300">皖嫂家政</a>
                    <a href="#qichuang" class="nav-link text-gray-800 hover:text-blue-600 transition-colors duration-300">启创产业园</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主视觉区域 -->
    <section class="relative h-screen overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"></div>
        <img src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80" 
             class="w-full h-full object-cover" alt="科技媒体公司形象">
        <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div class="text-center text-white">
                <h1 class="text-6xl font-bold mb-4">合论网络</h1>
                <p class="text-2xl">新媒体传播及数字化营销领军者</p>
                <div class="mt-8">
                    <a href="#about" class="tech-button px-8 py-3 rounded-full text-lg font-semibold inline-block">
                        了解更多
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- 关于我们和集团新闻 -->
    <section class="py-20 bg-gray-50" id="about">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-2 gap-8">
                <!-- 关于我们 -->
                <div class="bg-white rounded-lg overflow-hidden" id="about-card">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80" 
                             class="w-full h-48 object-cover transform hover:scale-110 transition-transform duration-500" alt="关于我们">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-bold mb-4 text-gray-800">关于我们</h3>
                        <p class="text-gray-600">合论网络是以内容为驱动的全媒体整合营销服务平台，被誉为“新媒体传播及数字化营销领军者”。公司旗下社区网站“合肥论坛”成立于2003年，是安徽人气第一的网络生活社区和城市门户网站。20余年精耕细作，匠心布局3+X的战略规划、构建四大核心服务体系，形成区域全媒体覆盖格局。新媒体时代，合论网络致力于以新媒体平台和内容创新为驱动，凭借深厚的粉丝基础、强大的流量平台矩阵以及专业化在地服务，为合作伙伴实现品效合一的营销服务，振兴地方品牌、拉动区域经济，为合肥城市发展注入新活力。
                        </p>
                    </div>
                </div>
                <!-- 集团新闻 -->
                <div class="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden group news-card" id="news">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1585776245991-cf89dd7fc73a?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80"
                             class="w-full h-48 object-cover transform group-hover:scale-110 transition-transform duration-700" alt="集团新闻">
                        <div class="absolute inset-0 bg-gradient-to-t from-blue-900/60 via-blue-600/30 to-transparent"></div>
                        <div class="absolute top-4 right-4">
                            <div class="bg-white/20 backdrop-blur-sm rounded-full p-2 animate-pulse">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-2xl font-bold text-gray-800 flex items-center">
                                <span class="w-1 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full mr-3 animate-pulse"></span>
                                集团新闻
                            </h3>
                            <div class="flex items-center text-sm text-gray-500 bg-gray-50 rounded-full px-3 py-1">
                                <svg class="w-4 h-4 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                实时更新
                            </div>
                        </div>
                        <div class="news-timeline">
                            <ul class="space-y-4">
                                <li class="news-item group/item relative pl-6 pb-4">
                                    <div class="absolute -left-2 top-1 w-4 h-4 bg-blue-500 rounded-full shadow-lg group-hover/item:scale-125 transition-transform duration-300 news-dot"></div>
                                    <a href="https://news.hefei.cc/2025/0411/030105940.shtml" target="_blank"
                                       class="block text-gray-700 hover:text-blue-600 transition-all duration-300 group-hover/item:translate-x-2">
                                        <span class="font-medium leading-relaxed">深化校企合作 共育传媒人才 | 徽商职业学院党委委员、纪委书记李莉一行到访合肥论坛</span>
                                        <div class="flex items-center justify-between mt-2">
                                            <div class="text-xs text-gray-400">2025-04-11</div>
                                            <div class="text-xs text-blue-500 opacity-0 group-hover/item:opacity-100 transition-opacity duration-300">阅读全文 →</div>
                                        </div>
                                    </a>
                                </li>
                                <li class="news-item group/item relative pl-6 pb-4">
                                    <div class="absolute -left-2 top-1 w-4 h-4 bg-green-500 rounded-full shadow-lg group-hover/item:scale-125 transition-transform duration-300 news-dot"></div>
                                    <a href="https://news.hefei.cc/2025/0314/030105726.shtml" target="_blank"
                                       class="block text-gray-700 hover:text-blue-600 transition-all duration-300 group-hover/item:translate-x-2">
                                        <span class="font-medium leading-relaxed">深化校企合作 共育传媒人才 | 安庆师范大学传媒学院执行院长王业明一行到访合肥论坛</span>
                                        <div class="flex items-center justify-between mt-2">
                                            <div class="text-xs text-gray-400">2025-03-14</div>
                                            <div class="text-xs text-blue-500 opacity-0 group-hover/item:opacity-100 transition-opacity duration-300">阅读全文 →</div>
                                        </div>
                                    </a>
                                </li>
                                <li class="news-item group/item relative pl-6 pb-4">
                                    <div class="absolute -left-2 top-1 w-4 h-4 bg-red-500 rounded-full shadow-lg group-hover/item:scale-125 transition-transform duration-300 news-dot"></div>
                                    <a href="https://news.hefei.cc/2025/0205/030105381.shtml" target="_blank"
                                       class="block text-gray-700 hover:text-blue-600 transition-all duration-300 group-hover/item:translate-x-2">
                                        <span class="font-medium leading-relaxed">蛇年启新程，合肥论坛开工大吉！</span>
                                        <div class="flex items-center justify-between mt-2">
                                            <div class="text-xs text-gray-400">2025-02-05</div>
                                            <div class="text-xs text-blue-500 opacity-0 group-hover/item:opacity-100 transition-opacity duration-300">阅读全文 →</div>
                                        </div>
                                    </a>
                                </li>
                                <li class="news-item group/item relative pl-6 pb-4">
                                    <div class="absolute -left-2 top-1 w-4 h-4 bg-purple-500 rounded-full shadow-lg group-hover/item:scale-125 transition-transform duration-300 news-dot"></div>
                                    <a href="https://news.hefei.cc/2025/0123/030105328.shtml" target="_blank"
                                       class="block text-gray-700 hover:text-blue-600 transition-all duration-300 group-hover/item:translate-x-2">
                                        <span class="font-medium leading-relaxed">合肥论坛荣膺"合肥文旅合伙人" 助力合肥文旅大发展</span>
                                        <div class="flex items-center justify-between mt-2">
                                            <div class="text-xs text-gray-400">2025-01-23</div>
                                            <div class="text-xs text-blue-500 opacity-0 group-hover/item:opacity-100 transition-opacity duration-300">阅读全文 →</div>
                                        </div>
                                    </a>
                                </li>
                                <li class="news-item group/item relative pl-6 pb-4">
                                    <div class="absolute -left-2 top-1 w-4 h-4 bg-yellow-500 rounded-full shadow-lg group-hover/item:scale-125 transition-transform duration-300 news-dot"></div>
                                    <a href="https://news.hefei.cc/2025/0123/030105325.shtml" target="_blank"
                                       class="block text-gray-700 hover:text-blue-600 transition-all duration-300 group-hover/item:translate-x-2">
                                        <span class="font-medium leading-relaxed">喜报！合肥论坛设计、视频、文创多件作品荣获省级大奖！</span>
                                        <div class="flex items-center justify-between mt-2">
                                            <div class="text-xs text-gray-400">2025-01-23</div>
                                            <div class="text-xs text-blue-500 opacity-0 group-hover/item:opacity-100 transition-opacity duration-300">阅读全文 →</div>
                                        </div>
                                    </a>
                                </li>
                                <li class="news-item group/item relative pl-6 pb-4">
                                    <div class="absolute -left-2 top-1 w-4 h-4 bg-indigo-500 rounded-full shadow-lg group-hover/item:scale-125 transition-transform duration-300 news-dot"></div>
                                    <a href="https://news.hefei.cc/2024/1231/030105132.shtml" target="_blank"
                                       class="block text-gray-700 hover:text-blue-600 transition-all duration-300 group-hover/item:translate-x-2">
                                        <span class="font-medium leading-relaxed">喜报！合论网络荣获两项省级荣誉！</span>
                                        <div class="flex items-center justify-between mt-2">
                                            <div class="text-xs text-gray-400">2024-12-31</div>
                                            <div class="text-xs text-blue-500 opacity-0 group-hover/item:opacity-100 transition-opacity duration-300">阅读全文 →</div>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="mt-6 pt-4 border-t border-gray-100">
                            <a href="#" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium transition-all duration-300 group/more hover:bg-blue-50 rounded-lg px-4 py-2 -mx-4 -my-2">
                                查看更多新闻
                                <svg class="w-4 h-4 ml-2 group-hover/more:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 四大能力 -->
    <section class="py-20 bg-white" id="capabilities">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-12">四大能力</h2>
            <div class="grid md:grid-cols-4 gap-8">
                <button class="tech-button rounded-lg p-6 transition duration-300 transform hover:scale-105">
                    <i class="fas fa-bullhorn text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold">新媒体运营能力</h3>
                </button>
                <button class="tech-button rounded-lg p-6 transition duration-300 transform hover:scale-105">
                    <i class="fas fa-lightbulb text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold">场景营销&IP打造能力</h3>
                </button>
                <button class="tech-button rounded-lg p-6 transition duration-300 transform hover:scale-105">
                    <i class="fas fa-code text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold">设计&技术研发能力</h3>
                </button>
                <button class="tech-button rounded-lg p-6 transition duration-300 transform hover:scale-105">
                    <i class="fas fa-video text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold">短视频&直播能力</h3>
                </button>
            </div>
        </div>
    </section>

    <!-- 合作客户 -->
    <section class="py-20 bg-gray-50" id="clients">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-12" id="clients-title">合作客户</h2>
            <div class="grid md:grid-cols-1 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-md flex items-center justify-center" id="clients-content">
                    <img src="img/kh.png" alt="合作客户" class="w-full">
                </div>
            </div>
        </div>
    </section>

    <!-- 集团荣誉 -->
    <section class="py-20 bg-white" id="honors">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-12" id="honors-title">集团荣誉</h2>
            <!-- 荣誉图片展示 -->
            <div class="grid md:grid-cols-4 gap-8 mb-12" id="honors-images">
                <div class="bg-white p-4 rounded-lg shadow-md" id="honor-img-1">
                    <img src="img/ry1.png" alt="集团荣誉1" class="w-full h-auto">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-md" id="honor-img-2">
                    <img src="img/ry2.png" alt="集团荣誉2" class="w-full h-auto">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-md" id="honor-img-3">
                    <img src="img/ry3.png" alt="集团荣誉3" class="w-full h-auto">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-md" id="honor-img-4">
                    <img src="img/ry4.png" alt="集团荣誉4" class="w-full h-auto">
                </div>
            </div>
            <!-- 荣誉列表 -->
            <div class="grid md:grid-cols-4 gap-8" id="honors-list">
                <div class="bg-white p-6 rounded-lg shadow-lg" id="honor-list-1">
                    <h3 class="text-xl font-bold text-gray-800 mb-4 text-center">国家级荣誉</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>• 国家高新技术企业</li>
                        <li>• 国家科技型中小企业</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg" id="honor-list-2">
                    <h3 class="text-xl font-bold text-gray-800 mb-4 text-center">省级荣誉</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>• 安徽省大数据企业</li>
                        <li>• 安徽省服务业标准化试点单位</li>
                        <li>• 安徽省 AAA 级信用等级诚信企业</li>
                        <li>• 安徽省广告经营诚信单位</li>
                        <li>• 安徽省皖美品牌示范企业</li>
                        <li>• 安徽省商标品牌示范企业</li>
                        <li>• 安徽省劳动保障诚信示范单位</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg" id="honor-list-3">
                    <h3 class="text-xl font-bold text-gray-800 mb-4 text-center">市级荣誉</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>• 合肥市"专精特新"企业</li>
                        <li>• 合肥市放心满意消费示范单位</li>
                        <li>• 合肥市创意文化产业链企业</li>
                        <li>• 合肥市中小企业公共服务示范平台</li>
                        <li>• 合肥市文化产业示范基地</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg" id="honor-list-4">
                    <h3 class="text-xl font-bold text-gray-800 mb-4 text-center">行业职务</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>• 安徽省新的社会阶层人士联谊会会长单位</li>
                        <li>• 合肥市互联网协会常务副会长单位</li>
                        <li>• 合肥市网络界人士联谊会会长单位</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- 皖嫂家政和启创产业园 -->
    <section class="py-20 bg-gray-50" id="services">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-2 gap-8">
                <!-- 皖嫂家政 -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden" id="wansao">
                    <img src="img/wsjz.png" 
                         class="w-full h-48 object-cover" alt="皖嫂家政">
                    <div class="p-6">
                        <h3 class="text-2xl font-bold mb-4">皖嫂家政</h3>
                        <p class="text-gray-600">安徽皖嫂巾帼家政服务中心是集母婴护理、家政服务、政企保洁、职业培训为一体的家政服务中心，中心由省妇联创办于2001年，至今已有17年发展历程。中心拥有一批经验丰富、技术过硬的优秀家政服务老师，先后服务了7万多家庭，培训了近万名家政服务人员。安徽皖嫂巾帼家政服务中心，经过多年发展，已经成为安徽家政服务行业领军者。 暖心“皖嫂”，服务万家！每一位皖嫂人将竭尽全力，为您和家人的优质生活而努力！</p>
                    </div>
                </div>
                <!-- 启创产业园 -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden" id="qichuang">
                    <img src="img/qccyy.png" 
                         class="w-full h-48 object-cover" alt="启创产业园">
                    <div class="p-6">
                        <h3 class="text-2xl font-bold mb-4">启创产业园</h3>
                        <p class="text-gray-600">启创孵化基地面积为20000平方米，基地内部配套设施齐全，有餐厅、多媒体会议室、创客咖啡厅、停车场等区域。园区可为创业者提供培训服务、人力服务、招商服务、政策服务、金融服务、投资服务、财税服务、法务服务等一站式的企业发展服务。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-gradient-to-r from-gray-900 to-gray-800 text-white py-12" id="footer">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-3 gap-8">
                <div class="tech-card bg-white/10 backdrop-blur-lg rounded-lg p-6">
                    <h4 class="text-xl font-bold mb-4">扫码关注合肥论坛</h4>
                    <img src="img/qrcode-placeholder.png" alt="合肥论坛公众号" class="w-32 mb-4">
                </div>
                <div class="tech-card bg-white/10 backdrop-blur-lg rounded-lg p-6">
                    <h4 class="text-xl font-bold mb-4">合论网络</h4>
                    <p>新媒体传播及数字化营销领军者</p>
                </div>
                <div class="tech-card bg-white/10 backdrop-blur-lg rounded-lg p-6">
                    <h4 class="text-xl font-bold mb-4">联系我们</h4>
                    <p>地址：安徽省合肥市蜀山区芙蓉路268号合肥创新创业园4b</p>
                    <p>电话：0551-64635820</p>
                    <p>官网：0551.com</p>
                </div>
            </div>
            <div class="mt-8 pt-8 border-t border-gray-700 text-center">
                <p>©2025 安徽肥肥网络传媒有限责任公司 版权所有 皖ICP备19004208号-5 网络文化经营许可证编号：皖网文(2022) 3214-050号</p>
            </div>
        </div>
    </footer>

    <!-- GSAP动画和交互效果 -->
    <script>
        // 注册ScrollTrigger插件
        gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);

        // 导航栏交互效果
        document.addEventListener('DOMContentLoaded', () => {
            // 移动端菜单
            const menuButton = document.querySelector('.menu-button');
            const navMenu = document.querySelector('.nav-menu');
            
            menuButton.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                menuButton.classList.toggle('active');
            });
            
            // 当前页面指示器
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');
            
            function setActiveLink() {
                const scrollPosition = window.scrollY;
                
                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 100;
                    const sectionHeight = section.offsetHeight;
                    const sectionId = section.getAttribute('id');
                    
                    if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                        navLinks.forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === `#${sectionId}`) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            }
            
            window.addEventListener('scroll', setActiveLink);
            
            // 点击导航链接时关闭移动端菜单并平滑滚动
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const href = link.getAttribute('href');
                    if (href === '#') {
                        // 处理首页链接
                        gsap.to(window, {
                            duration: 0.8,
                            scrollTo: {
                                y: 0,
                                offsetY: 0
                            },
                            ease: 'power2.inOut'
                        });
                    } else {
                        const target = document.querySelector(href);
                        if (target) {
                            gsap.to(window, {
                                duration: 0.8,
                                scrollTo: {
                                    y: target,
                                    offsetY: 70
                                },
                                ease: 'power2.inOut'
                            });
                        }
                    }
                    navMenu.classList.remove('active');
                    menuButton.classList.remove('active');
                });
            });
        });

        // 导航栏滚动效果
        window.addEventListener('scroll', () => {
            const nav = document.querySelector('nav');
            if (window.scrollY > 50) {
                nav.style.background = 'rgba(255, 255, 255, 0.95)';
                nav.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
            } else {
                nav.style.background = 'linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85))';
                nav.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.05)';
            }
        });

        // 关于我们和集团新闻卡片动画
        gsap.to('#about-card', {
            scrollTrigger: {
                trigger: '#about',
                start: 'top bottom-=50',
                toggleActions: 'play none none reverse'
            },
            x: 0,
            opacity: 1,
            duration: 0.8,
            ease: 'none'
        });

        gsap.to('#news', {
            scrollTrigger: {
                trigger: '#news',
                start: 'top bottom-=50',
                toggleActions: 'play none none reverse'
            },
            x: 0,
            opacity: 1,
            duration: 0.8,
            ease: 'none'
        });

        // 集团新闻列表项动画
        gsap.utils.toArray('#news li').forEach((item, index) => {
            gsap.from(item, {
                scrollTrigger: {
                    trigger: item,
                    start: 'top bottom-=30',
                    toggleActions: 'play none none reverse'
                },
                x: -30,
                opacity: 0,
                duration: 0.6,
                delay: index * 0.1,
                ease: 'power2.out'
            });
        });

        // 四大能力标题动画
        gsap.to('#capabilities h2', {
            scrollTrigger: {
                trigger: '#capabilities',
                start: 'top bottom-=50',
                toggleActions: 'play none none reverse'
            },
            y: 0,
            opacity: 1,
            duration: 0.6,
            ease: 'none'
        });

        // 四大能力卡片动画
        gsap.utils.toArray('#capabilities button').forEach((button, index) => {
            gsap.to(button, {
                scrollTrigger: {
                    trigger: button,
                    start: 'top bottom-=50',
                    toggleActions: 'play none none reverse'
                },
                y: 0,
                opacity: 1,
                duration: 0.6,
                delay: index * 0.1,
                ease: 'none'
            });
        });

        // 合作客户动画
        gsap.from('#clients-title', {
            scrollTrigger: {
                trigger: '#clients',
                start: 'top bottom-=50',
                toggleActions: 'play none none reverse'
            },
            y: 20,
            opacity: 0,
            duration: 0.6,
            ease: 'none'
        });

        gsap.from('#clients-content', {
            scrollTrigger: {
                trigger: '#clients',
                start: 'top bottom-=50',
                toggleActions: 'play none none reverse'
            },
            y: 30,
            opacity: 0,
            duration: 0.8,
            ease: 'none'
        });

        // 集团荣誉动画
        gsap.from('#honors-title', {
            scrollTrigger: {
                trigger: '#honors',
                start: 'top bottom-=50',
                toggleActions: 'play none none reverse'
            },
            y: 20,
            opacity: 0,
            duration: 0.6,
            ease: 'none'
        });

        // 荣誉图片动画
        ['#honor-img-1', '#honor-img-2', '#honor-img-3', '#honor-img-4'].forEach((id, index) => {
            gsap.from(id, {
                scrollTrigger: {
                    trigger: id,
                    start: 'top bottom-=50',
                    toggleActions: 'play none none reverse'
                },
                y: 30,
                opacity: 0,
                duration: 0.6,
                delay: index * 0.1,
                ease: 'none'
            });
        });

        // 荣誉列表动画
        ['#honor-list-1', '#honor-list-2', '#honor-list-3', '#honor-list-4'].forEach((id, index) => {
            gsap.from(id, {
                scrollTrigger: {
                    trigger: id,
                    start: 'top bottom-=50',
                    toggleActions: 'play none none reverse'
                },
                y: 30,
                opacity: 0,
                duration: 0.6,
                delay: index * 0.1,
                ease: 'none'
            });
        });

        // 皖嫂家政和启创产业园动画
        gsap.to('#wansao', {
            scrollTrigger: {
                trigger: '#wansao',
                start: 'top bottom-=50',
                toggleActions: 'play none none reverse'
            },
            x: 0,
            opacity: 1,
            duration: 0.8,
            ease: 'none'
        });

        gsap.to('#qichuang', {
            scrollTrigger: {
                trigger: '#qichuang',
                start: 'top bottom-=50',
                toggleActions: 'play none none reverse'
            },
            x: 0,
            opacity: 1,
            duration: 0.8,
            ease: 'none'
        });
    </script>
</body>
</html>
