我想开发一个{合论网络官网，顶部tab栏为首页、关于我们、集团新闻、四大能力、合肥论坛、合肥热线、皖嫂家政、启创产业园。网站从上到下分为7部分。第1部分为一张科技媒体公司的大形象图。第2部分分成左右两个卡片，左边卡片小标题是关于我们，右边卡片是集团新闻，卡片内容均为一张图和文字介绍。第3部分是四大能力，内容包含4个按钮，分别为新媒体运营能力、场景营销&IP打造能力、设计&技术研发能力、短视频&直播能力。第4部分为合作客户。第5部分是集团荣誉，集团荣誉为ry1.png到ry4.png，以及以下内容（国家高新技术企业、国家科技型中小企业、安徽省大数据企业、安徽省服务业标准化试点单位、安徽省 AAA 级信用等级诚信企业、安徽省广告经营诚信单位、安徽省皖美品牌示范企业、安徽省商标品牌示范企业、安徽省劳动保障诚信示范单位、合肥市“专精特新”企业、合肥市放心满意消费示范单位、合肥市创意文化产业链企业、合肥市中小企业公共服务示范平台、合肥市文化产业示范基地、安徽省新的社会阶层人士联谊会会长单位、合肥市互联网协会常务副会长单位、合肥市网络界人士联谊会会长单位）。第6部分分成左右两个卡片，左边卡片小标题是皖嫂家政，右边卡片启创产业园，卡片内容均为一张图和文字介绍。第7部分为footer，包含合肥论坛公众号二维码、公司名称、地址、版权信息、0551.com}，现在需要输出高保真的原型图，请通过以下方式帮我完成所有界面的原型设计，并确保这些原型界面可以直接用于开发：1、用户体验分析：先分析这个网站的主要功能和用户需求，确定核心交互逻辑。2、产品界面规划：作为产品经理，定义关键界面，确保信息架构合理。3、高保真 UI 设计：作为 UI 设计师，设计贴近真实 Web 设计规范的界面，使用现代化的 UI 元素，使其具有良好的视觉体验。4、HTML 原型实现：使用 HTML + Tailwind CSS（或 Bootstrap）生成原型界面，并使用 FontAwesome（或其他开源 UI 组件）让界面更加精美、接近真实的 App 设计。5、使用真实的 UI 图片，而非占位符图片（可从 Unsplash、Pexels、Apple 官方 UI 资源中选择）。6、要求官网整体风格为科技媒体公司。请按照以上要求生成完整的 HTML 代码，并确保其可用于实际开发。